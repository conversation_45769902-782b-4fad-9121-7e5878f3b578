[{"name": "Process Start", "start": 1748263098656, "end": 1748263104840, "duration": 6184, "pid": 34428, "index": 0}, {"name": "Application Start", "start": 1748263104842, "end": 1748263107183, "duration": 2341, "pid": 34428, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748263104869, "end": 1748263104919, "duration": 50, "pid": 34428, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748263104919, "end": 1748263104984, "duration": 65, "pid": 34428, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748263104921, "end": 1748263104922, "duration": 1, "pid": 34428, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748263104924, "end": 1748263104925, "duration": 1, "pid": 34428, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748263104926, "end": 1748263104927, "duration": 1, "pid": 34428, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748263104929, "end": 1748263104929, "duration": 0, "pid": 34428, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748263104931, "end": 1748263104932, "duration": 1, "pid": 34428, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748263104933, "end": 1748263104934, "duration": 1, "pid": 34428, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748263104935, "end": 1748263104936, "duration": 1, "pid": 34428, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748263104937, "end": 1748263104938, "duration": 1, "pid": 34428, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748263104939, "end": 1748263104939, "duration": 0, "pid": 34428, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748263104941, "end": 1748263104941, "duration": 0, "pid": 34428, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748263104942, "end": 1748263104943, "duration": 1, "pid": 34428, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748263104944, "end": 1748263104946, "duration": 2, "pid": 34428, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748263104947, "end": 1748263104948, "duration": 1, "pid": 34428, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748263104949, "end": 1748263104950, "duration": 1, "pid": 34428, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748263104951, "end": 1748263104951, "duration": 0, "pid": 34428, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748263104952, "end": 1748263104953, "duration": 1, "pid": 34428, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748263104954, "end": 1748263104954, "duration": 0, "pid": 34428, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748263104955, "end": 1748263104956, "duration": 1, "pid": 34428, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748263104956, "end": 1748263104957, "duration": 1, "pid": 34428, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748263104958, "end": 1748263104959, "duration": 1, "pid": 34428, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748263104960, "end": 1748263104961, "duration": 1, "pid": 34428, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748263104965, "end": 1748263104965, "duration": 0, "pid": 34428, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748263104967, "end": 1748263104968, "duration": 1, "pid": 34428, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748263104970, "end": 1748263104971, "duration": 1, "pid": 34428, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748263104974, "end": 1748263104975, "duration": 1, "pid": 34428, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748263104983, "end": 1748263104984, "duration": 1, "pid": 34428, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748263104984, "end": 1748263104984, "duration": 0, "pid": 34428, "index": 30}, {"name": "Load extend/agent.js", "start": 1748263104986, "end": 1748263105119, "duration": 133, "pid": 34428, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748263104988, "end": 1748263104991, "duration": 3, "pid": 34428, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748263104994, "end": 1748263105102, "duration": 108, "pid": 34428, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748263105103, "end": 1748263105106, "duration": 3, "pid": 34428, "index": 34}, {"name": "Load extend/context.js", "start": 1748263105119, "end": 1748263105288, "duration": 169, "pid": 34428, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748263105120, "end": 1748263105151, "duration": 31, "pid": 34428, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748263105152, "end": 1748263105158, "duration": 6, "pid": 34428, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748263105159, "end": 1748263105160, "duration": 1, "pid": 34428, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748263105165, "end": 1748263105230, "duration": 65, "pid": 34428, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748263105234, "end": 1748263105242, "duration": 8, "pid": 34428, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748263105250, "end": 1748263105251, "duration": 1, "pid": 34428, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748263105254, "end": 1748263105266, "duration": 12, "pid": 34428, "index": 42}, {"name": "Load agent.js", "start": 1748263105288, "end": 1748263105426, "duration": 138, "pid": 34428, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748263105290, "end": 1748263105293, "duration": 3, "pid": 34428, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748263105300, "end": 1748263105301, "duration": 1, "pid": 34428, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748263105303, "end": 1748263105334, "duration": 31, "pid": 34428, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748263105335, "end": 1748263105339, "duration": 4, "pid": 34428, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748263105340, "end": 1748263105372, "duration": 32, "pid": 34428, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748263105373, "end": 1748263105373, "duration": 0, "pid": 34428, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748263105375, "end": 1748263105376, "duration": 1, "pid": 34428, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748263105381, "end": 1748263105411, "duration": 30, "pid": 34428, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748263105412, "end": 1748263105423, "duration": 11, "pid": 34428, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748263105424, "end": 1748263105424, "duration": 0, "pid": 34428, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748263105438, "end": 1748263106738, "duration": 1300, "pid": 34428, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748263105439, "end": 1748263106664, "duration": 1225, "pid": 34428, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748263105439, "end": 1748263107135, "duration": 1696, "pid": 34428, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748263106389, "end": 1748263106516, "duration": 127, "pid": 34428, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748263106420, "end": 1748263106726, "duration": 306, "pid": 34428, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748263106553, "end": 1748263107169, "duration": 616, "pid": 34428, "index": 59}]