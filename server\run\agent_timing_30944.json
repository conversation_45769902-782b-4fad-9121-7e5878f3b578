[{"name": "Process Start", "start": 1748262314352, "end": 1748262321198, "duration": 6846, "pid": 30944, "index": 0}, {"name": "Application Start", "start": 1748262321201, "end": 1748262324912, "duration": 3711, "pid": 30944, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748262321246, "end": 1748262321305, "duration": 59, "pid": 30944, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748262321305, "end": 1748262321371, "duration": 66, "pid": 30944, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748262321308, "end": 1748262321309, "duration": 1, "pid": 30944, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748262321312, "end": 1748262321313, "duration": 1, "pid": 30944, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748262321314, "end": 1748262321315, "duration": 1, "pid": 30944, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748262321316, "end": 1748262321316, "duration": 0, "pid": 30944, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748262321318, "end": 1748262321318, "duration": 0, "pid": 30944, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748262321319, "end": 1748262321320, "duration": 1, "pid": 30944, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748262321320, "end": 1748262321321, "duration": 1, "pid": 30944, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748262321322, "end": 1748262321323, "duration": 1, "pid": 30944, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748262321325, "end": 1748262321326, "duration": 1, "pid": 30944, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748262321327, "end": 1748262321327, "duration": 0, "pid": 30944, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748262321328, "end": 1748262321329, "duration": 1, "pid": 30944, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748262321330, "end": 1748262321331, "duration": 1, "pid": 30944, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748262321332, "end": 1748262321332, "duration": 0, "pid": 30944, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748262321333, "end": 1748262321334, "duration": 1, "pid": 30944, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748262321335, "end": 1748262321336, "duration": 1, "pid": 30944, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748262321337, "end": 1748262321338, "duration": 1, "pid": 30944, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748262321338, "end": 1748262321339, "duration": 1, "pid": 30944, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748262321342, "end": 1748262321343, "duration": 1, "pid": 30944, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748262321344, "end": 1748262321344, "duration": 0, "pid": 30944, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748262321346, "end": 1748262321347, "duration": 1, "pid": 30944, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748262321349, "end": 1748262321350, "duration": 1, "pid": 30944, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748262321353, "end": 1748262321353, "duration": 0, "pid": 30944, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748262321355, "end": 1748262321355, "duration": 0, "pid": 30944, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748262321360, "end": 1748262321361, "duration": 1, "pid": 30944, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748262321365, "end": 1748262321366, "duration": 1, "pid": 30944, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748262321370, "end": 1748262321370, "duration": 0, "pid": 30944, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748262321370, "end": 1748262321370, "duration": 0, "pid": 30944, "index": 30}, {"name": "Load extend/agent.js", "start": 1748262321372, "end": 1748262321832, "duration": 460, "pid": 30944, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748262321375, "end": 1748262321378, "duration": 3, "pid": 30944, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748262321381, "end": 1748262321736, "duration": 355, "pid": 30944, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748262321752, "end": 1748262321782, "duration": 30, "pid": 30944, "index": 34}, {"name": "Load extend/context.js", "start": 1748262321832, "end": 1748262322351, "duration": 519, "pid": 30944, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748262321834, "end": 1748262321950, "duration": 116, "pid": 30944, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748262321961, "end": 1748262321972, "duration": 11, "pid": 30944, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748262321981, "end": 1748262321982, "duration": 1, "pid": 30944, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748262321987, "end": 1748262322152, "duration": 165, "pid": 30944, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748262322178, "end": 1748262322316, "duration": 138, "pid": 30944, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748262322319, "end": 1748262322320, "duration": 1, "pid": 30944, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748262322325, "end": 1748262322333, "duration": 8, "pid": 30944, "index": 42}, {"name": "Load agent.js", "start": 1748262322352, "end": 1748262322709, "duration": 357, "pid": 30944, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748262322355, "end": 1748262322358, "duration": 3, "pid": 30944, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748262322364, "end": 1748262322364, "duration": 0, "pid": 30944, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748262322366, "end": 1748262322398, "duration": 32, "pid": 30944, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748262322399, "end": 1748262322404, "duration": 5, "pid": 30944, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748262322407, "end": 1748262322600, "duration": 193, "pid": 30944, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748262322601, "end": 1748262322603, "duration": 2, "pid": 30944, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748262322609, "end": 1748262322611, "duration": 2, "pid": 30944, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748262322617, "end": 1748262322688, "duration": 71, "pid": 30944, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748262322690, "end": 1748262322703, "duration": 13, "pid": 30944, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748262322704, "end": 1748262322708, "duration": 4, "pid": 30944, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748262322725, "end": 1748262324342, "duration": 1617, "pid": 30944, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748262322726, "end": 1748262324265, "duration": 1539, "pid": 30944, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748262322727, "end": 1748262324909, "duration": 2182, "pid": 30944, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748262323801, "end": 1748262324058, "duration": 257, "pid": 30944, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748262323853, "end": 1748262324333, "duration": 480, "pid": 30944, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748262324104, "end": 1748262324849, "duration": 745, "pid": 30944, "index": 59}]