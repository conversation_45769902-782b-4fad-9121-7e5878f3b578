// 更新用户角色的脚本
'use strict';

const mysql = require('mysql2/promise');

async function updateUserRole() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: 'root',
      database: 'stock_analysis'
    });

    console.log('✅ 数据库连接成功');

    // 查询所有用户
    console.log('\n📋 当前用户列表：');
    const [users] = await connection.execute('SELECT id, username, email, role, status FROM users');
    
    users.forEach(user => {
      console.log(`ID: ${user.id}, 用户名: ${user.username}, 邮箱: ${user.email}, 角色: ${user.role}, 状态: ${user.status}`);
    });

    // 更新 admin2 用户的角色为 admin
    console.log('\n🔄 更新 admin2 用户角色为 admin...');
    const [result] = await connection.execute(
      'UPDATE users SET role = ? WHERE username = ?',
      ['admin', 'admin2']
    );

    if (result.affectedRows > 0) {
      console.log('✅ 用户角色更新成功！');
      
      // 验证更新结果
      const [updatedUser] = await connection.execute(
        'SELECT id, username, email, role, status FROM users WHERE username = ?',
        ['admin2']
      );
      
      if (updatedUser.length > 0) {
        console.log('📋 更新后的用户信息：');
        const user = updatedUser[0];
        console.log(`ID: ${user.id}, 用户名: ${user.username}, 邮箱: ${user.email}, 角色: ${user.role}, 状态: ${user.status}`);
      }
    } else {
      console.log('❌ 没有找到用户 admin2');
    }

    // 如果存在 admin 用户，也尝试重置其密码
    console.log('\n🔍 检查是否存在 admin 用户...');
    const [adminUsers] = await connection.execute(
      'SELECT id, username, email, role, status FROM users WHERE username = ?',
      ['admin']
    );

    if (adminUsers.length > 0) {
      console.log('📋 找到 admin 用户：');
      const admin = adminUsers[0];
      console.log(`ID: ${admin.id}, 用户名: ${admin.username}, 邮箱: ${admin.email}, 角色: ${admin.role}, 状态: ${admin.status}`);
      
      // 重置 admin 用户密码为 123456 的哈希值
      const crypto = require('crypto');
      const hashedPassword = crypto.createHash('sha256').update('123456').digest('hex');
      
      console.log('\n🔄 重置 admin 用户密码...');
      const [updateResult] = await connection.execute(
        'UPDATE users SET password = ? WHERE username = ?',
        [hashedPassword, 'admin']
      );
      
      if (updateResult.affectedRows > 0) {
        console.log('✅ admin 用户密码重置成功！密码: 123456');
      }
    } else {
      console.log('❌ 没有找到 admin 用户');
    }

  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n📤 数据库连接已关闭');
    }
  }
}

// 运行脚本
updateUserRole().catch(console.error);
