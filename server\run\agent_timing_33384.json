[{"name": "Process Start", "start": 1748262933821, "end": 1748262938888, "duration": 5067, "pid": 33384, "index": 0}, {"name": "Application Start", "start": 1748262938890, "end": 1748262941299, "duration": 2409, "pid": 33384, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748262938916, "end": 1748262938948, "duration": 32, "pid": 33384, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748262938948, "end": 1748262939002, "duration": 54, "pid": 33384, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748262938949, "end": 1748262938949, "duration": 0, "pid": 33384, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748262938951, "end": 1748262938952, "duration": 1, "pid": 33384, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748262938953, "end": 1748262938953, "duration": 0, "pid": 33384, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748262938954, "end": 1748262938954, "duration": 0, "pid": 33384, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748262938955, "end": 1748262938956, "duration": 1, "pid": 33384, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748262938957, "end": 1748262938957, "duration": 0, "pid": 33384, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748262938958, "end": 1748262938958, "duration": 0, "pid": 33384, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748262938959, "end": 1748262938960, "duration": 1, "pid": 33384, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748262938961, "end": 1748262938961, "duration": 0, "pid": 33384, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748262938962, "end": 1748262938963, "duration": 1, "pid": 33384, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748262938964, "end": 1748262938964, "duration": 0, "pid": 33384, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748262938965, "end": 1748262938966, "duration": 1, "pid": 33384, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748262938967, "end": 1748262938967, "duration": 0, "pid": 33384, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748262938968, "end": 1748262938969, "duration": 1, "pid": 33384, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748262938970, "end": 1748262938970, "duration": 0, "pid": 33384, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748262938971, "end": 1748262938972, "duration": 1, "pid": 33384, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748262938973, "end": 1748262938973, "duration": 0, "pid": 33384, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748262938974, "end": 1748262938975, "duration": 1, "pid": 33384, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748262938976, "end": 1748262938977, "duration": 1, "pid": 33384, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748262938977, "end": 1748262938978, "duration": 1, "pid": 33384, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748262938978, "end": 1748262938979, "duration": 1, "pid": 33384, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748262938991, "end": 1748262938991, "duration": 0, "pid": 33384, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748262938993, "end": 1748262938993, "duration": 0, "pid": 33384, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748262938995, "end": 1748262938995, "duration": 0, "pid": 33384, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748262938998, "end": 1748262938998, "duration": 0, "pid": 33384, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748262939001, "end": 1748262939002, "duration": 1, "pid": 33384, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748262939002, "end": 1748262939002, "duration": 0, "pid": 33384, "index": 30}, {"name": "Load extend/agent.js", "start": 1748262939003, "end": 1748262939197, "duration": 194, "pid": 33384, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748262939005, "end": 1748262939009, "duration": 4, "pid": 33384, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748262939011, "end": 1748262939175, "duration": 164, "pid": 33384, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748262939179, "end": 1748262939183, "duration": 4, "pid": 33384, "index": 34}, {"name": "Load extend/context.js", "start": 1748262939198, "end": 1748262939354, "duration": 156, "pid": 33384, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748262939199, "end": 1748262939229, "duration": 30, "pid": 33384, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748262939230, "end": 1748262939236, "duration": 6, "pid": 33384, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748262939238, "end": 1748262939239, "duration": 1, "pid": 33384, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748262939241, "end": 1748262939328, "duration": 87, "pid": 33384, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748262939331, "end": 1748262939335, "duration": 4, "pid": 33384, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748262939338, "end": 1748262939339, "duration": 1, "pid": 33384, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748262939340, "end": 1748262939345, "duration": 5, "pid": 33384, "index": 42}, {"name": "Load agent.js", "start": 1748262939354, "end": 1748262939519, "duration": 165, "pid": 33384, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748262939355, "end": 1748262939356, "duration": 1, "pid": 33384, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748262939357, "end": 1748262939358, "duration": 1, "pid": 33384, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748262939360, "end": 1748262939410, "duration": 50, "pid": 33384, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748262939411, "end": 1748262939413, "duration": 2, "pid": 33384, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748262939414, "end": 1748262939440, "duration": 26, "pid": 33384, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748262939442, "end": 1748262939444, "duration": 2, "pid": 33384, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748262939446, "end": 1748262939447, "duration": 1, "pid": 33384, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748262939451, "end": 1748262939506, "duration": 55, "pid": 33384, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748262939507, "end": 1748262939517, "duration": 10, "pid": 33384, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748262939518, "end": 1748262939518, "duration": 0, "pid": 33384, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748262939532, "end": 1748262941016, "duration": 1484, "pid": 33384, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748262939534, "end": 1748262940971, "duration": 1437, "pid": 33384, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748262939534, "end": 1748262941272, "duration": 1738, "pid": 33384, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748262940583, "end": 1748262940770, "duration": 187, "pid": 33384, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748262940650, "end": 1748262941012, "duration": 362, "pid": 33384, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748262940818, "end": 1748262941292, "duration": 474, "pid": 33384, "index": 59}]